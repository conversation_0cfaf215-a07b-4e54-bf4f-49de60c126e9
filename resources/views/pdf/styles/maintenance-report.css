/* Estilos para el reporte de mantenimiento de vehículos */

/* Estilos generales */
body {
    font-family: Arial, sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    margin: 0;
    padding: 20px;
}

/* <PERSON><PERSON><PERSON><PERSON> principal */
.main-title {
    text-align: center;
    margin-bottom: 20px;
    margin-top: -60px;
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
}

/* Información del vehículo */
.vehicle-info {
    border: 1px solid #e0e0e0;
    padding: 12px;
    border-radius: 6px;
    background-color: #fdfdfd;
    margin-bottom: 20px;
}

.vehicle-info-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 6px;
    color: #111;
    border-bottom: 1px solid #ddd;
    padding-bottom: 4px;
}

.vehicle-info-item {
    margin-bottom: 2px;
}

.vehicle-info-item strong {
    color: #2c3e50;
}

/* Tabla de mantenimientos */
.maintenance-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 11px;
}

.maintenance-table thead tr {
    background-color: #f5f5f5;
    font-weight: bold;
}

.maintenance-table th,
.maintenance-table td {
    border: 1px solid #ddd;
    padding: 6px;
}

.maintenance-table th {
    padding: 8px;
    font-size: 12px;
    color: #2c3e50;
}

.maintenance-table .text-left {
    text-align: left;
}

.maintenance-table .text-center {
    text-align: center;
}

.maintenance-table .text-right {
    text-align: right;
}

.maintenance-table .no-records {
    padding: 12px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.maintenance-table tfoot tr {
    background-color: #f9f9f9;
    font-weight: bold;
}

.maintenance-table tfoot td {
    padding: 8px;
    font-size: 12px;
}

/* Sección de imágenes */
.images-section {
    page-break-before: always;
}

.images-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
}

.images-container {
    width: 100%;
    text-align: center;
}

.maintenance-images {
    margin-bottom: 30px;
    page-break-inside: avoid;
}

.maintenance-images h5 {
    margin-bottom: 10px;
    color: #333;
    font-size: 14px;
    font-weight: bold;
}

.image-container {
    margin-bottom: 15px;
}

.maintenance-image {
    width: 85%;
    max-width: 450px;
    border: 2px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Utilidades */
.page-break {
    page-break-before: always;
}

.no-page-break {
    page-break-inside: avoid;
}

.text-bold {
    font-weight: bold;
}

.text-muted {
    color: #666;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-15 {
    margin-bottom: 15px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-30 {
    margin-bottom: 30px;
}
