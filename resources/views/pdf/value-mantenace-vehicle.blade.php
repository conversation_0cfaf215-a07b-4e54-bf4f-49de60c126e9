<x-layout>
    <style>
        {!! file_get_contents(resource_path('views/pdf/styles/maintenance-report.css')) !!}
    </style>

    <div class="main-title">
        <h3>Valor de Mantenimiento del Vehículo</h3>
    </div>

    <!-- Información del vehículo -->
    <div class="vehicle-info">
        <div class="vehicle-info-title">
            Información del Vehículo
        </div>
        <div class="vehicle-info-item"><strong>Placa:</strong> {{ $record->placa }}</div>
        <div class="vehicle-info-item"><strong>Marca:</strong> {{ $record->marca ?? '-' }}</div>
        <div class="vehicle-info-item"><strong>Unidad:</strong> {{ $record->unidad ?? '-' }}</div>
        <div class="vehicle-info-item"><strong>Tarjeta Propiedad:</strong> {{ $record->property_card ?? '-' }}</div>
        <div class="vehicle-info-item"><strong>Fecha:</strong> {{ now()->format('d/m/Y') }}</div>
    </div>

    <!-- Tabla de mantenimientos -->
    <table class="maintenance-table">
        <thead>
            <tr>
                <th class="text-left">Descripción</th>
                <th class="text-center">Kilometraje</th>
                <th class="text-right">Precio Material</th>
                <th class="text-right">Mano de Obra</th>
                <th class="text-right">Costo Total</th>
            </tr>
        </thead>
        <tbody>
            @forelse($record->maintenances as $item)
            <tr>
                <td class="text-left">{{ $item->maintenanceItem->name ?? 'N/A' }}</td>
                <td class="text-center">{{ \App\Helpers\PdfHelper::formatMileage($item->mileage) }}</td>
                <td class="text-right">{{ \App\Helpers\PdfHelper::formatCurrency($item->Price_material) }}</td>
                <td class="text-right">{{ \App\Helpers\PdfHelper::formatCurrency($item->workforce) }}</td>
                <td class="text-right">{{ \App\Helpers\PdfHelper::formatCurrency($item->maintenance_cost) }}</td>
            </tr>
            @empty
            <tr>
                <td colspan="5" class="no-records">
                    No hay registros de mantenimiento
                </td>
            </tr>
            @endforelse
        </tbody>
        <tfoot>
            <tr>
                <td colspan="4" class="text-right text-bold">Total General:</td>
                <td class="text-right text-bold">{{ \App\Helpers\PdfHelper::formatCurrency($total) }}</td>
            </tr>
        </tfoot>
    </table>

    <!-- Sección de imágenes (solo si hay imágenes) -->
    @if($processedMaintenances->isNotEmpty())
        <div class="images-section">
            <div class="images-title">
                <h4>Evidencias Fotográficas</h4>
            </div>

            <div class="images-container">
                @foreach($processedMaintenances as $processedItem)
                    @if(!empty($processedItem['images']))
                        <div class="maintenance-images">
                            <h5>{{ $processedItem['maintenance']->maintenanceItem->name ?? 'Mantenimiento' }}</h5>

                            @foreach($processedItem['images'] as $image)
                                @if($image)
                                    <div class="image-container">
                                        <img src="data:{{ $image['mime'] }};base64,{{ $image['data'] }}"
                                             class="maintenance-image"
                                             alt="Evidencia de mantenimiento">
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
    @endif

</x-layout>