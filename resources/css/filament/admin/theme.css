@import '../../../../vendor/filament/filament/resources/css/theme.css';
/* @import '../../vendor/alperenersoy/filament-export/resources/css/filament-export.css'; */
@import 'login.css';
@import 'custom-data-table.css';
@import 'custom-section.css';
@import 'form-fields.css';
@import 'modal.css';
@import 'report-card.css';
@import 'tooltip.css';
@import 'top-navigation.css';

@config 'tailwind.config.js';


.es-file-upload.document-logo>div {
    @apply w-48;
}

.es-file-upload.document-logo-preview>div {
    @apply w-40;
}

.fi-in-text-item .group-hover\/item\:underline,
.fi-ta-text-item .group-hover\/item\:underline {
    @apply text-primary-600 dark:text-primary-400 font-semibold;
}

.fi-sidebar-nav {
    scrollbar-width: thin;
}

.fi-ta-empty-state-icon-ctn {
    @apply bg-platinum;
}

.fi-dropdown-panel {
    @apply divide-gray-200/80;
}

.fi-badge {
    display: inline-flex;
}

/* End Alignment sortable column enhancement */
.fi-ta-header-cell:has(button.justify-end > svg.fi-ta-header-cell-sort-icon) {
    padding-right: 0;
}

:not(.dark) .fi-body {
    position: relative;
    background-color: #E8E9EB;
    z-index: 1;
}

:not(.dark) .fi-body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(99.6deg,
            rgba(232, 233, 235, 1) 10.6%,
            rgba(240, 241, 243, 1) 32.9%,
            rgba(248, 249, 251, 0.7) 50%,
            rgba(240, 241, 243, 1) 67.1%,
            rgba(232, 233, 235, 1) 83.4%);
    pointer-events: none;
    z-index: -1;
}

.fi-ta-table:has(.is-spreadsheet) {
    .fi-ta-row {
        @apply divide-x divide-gray-200 dark:divide-gray-700;
    }

    .fi-ta-summary-row-heading {
        @apply px-3 py-3 !important;
    }

    .fi-ta-text-summary {
        @apply px-3 py-3 !important;

        >span {
            @apply font-medium text-gray-950 dark:text-white;
        }
    }

    .fi-ta-text {
        @apply px-3 py-0 !important;
    }

    .fi-ta-text-input {
        @apply px-0 py-0 !important;
    }

    .fi-ta-icon {
        @apply px-3 py-0 !important;
    }

    .fi-ta-text-input {

        .fi-input-wrp,
        .fi-input {
            @apply ring-0 bg-transparent shadow-none rounded-none !important;
        }
    }

    .fi-ta-cell:has(.fi-ta-text-input):focus-within {
        outline: 2px solid #2563eb;
        outline-offset: -2px;
        z-index: 1;
    }

    .fi-ta-col-wrp button:focus {
        outline: none !important;
        box-shadow: none !important;
    }
}

.doc-template-container {
    color: initial;
}

.doc-template-container .overflow-x-auto,
.doc-template-paper {
    scrollbar-width: thin;
}

.doc-template-paper.preview {
    zoom: 0.5;
}

@media (min-width: 1024px) {
    .doc-template-paper.preview {
        zoom: 0.85;
    }
}

.doc-template-paper:not(.preview) {
    zoom: 0.65;
}

@media (min-width: 1024px) {
    .doc-template-paper:not(.preview) {
        zoom: 1;
    }
}