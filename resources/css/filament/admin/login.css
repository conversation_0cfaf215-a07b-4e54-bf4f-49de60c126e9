explicame los style de filament php detalladamente

/* Whole page wrapper */
.custom-auth-wrapper {
    @apply min-h-screen flex flex-col lg:flex-row items-stretch bg-gray-50 dark:bg-gray-900;
}

/* Empty panel wrapper - with background image */
.custom-auth-empty-panel {
    @apply hidden lg:block lg:w-1/2 xl:w-3/5 bg-indigo-600 bg-cover bg-center bg-no-repeat relative;
    background-image: url('/img/page.jpg');
}


/* Overlay for the image panel - Fixed version */
.custom-auth-empty-panel::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: rgb(67 56 202 / 0.6);
    /* bg-indigo-900/60 */
    z-index: 0;
}

/* Content inside the image panel */
.custom-auth-empty-panel-content {
    @apply relative z-10 flex flex-col items-center justify-center h-full text-white p-4 sm:p-6 lg:p-8 xl:p-12;
}

/* Form panel wrapper */
.custom-auth-form-panel {
    @apply bg-white dark:bg-gray-800 w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center py-8 sm:py-12;
}

/* Form wrapper */
.custom-auth-form-wrapper {
    @apply px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12 w-full max-w-md mx-auto space-y-6 sm:space-y-8;
}

/* Form header */
.custom-auth-form-header {
    @apply text-center space-y-2 mb-4 sm:mb-6;
}

/* Form logo */
.custom-auth-form-logo {
    @apply mx-auto h-10 sm:h-12 w-auto;
}

/* Form title */
.custom-auth-form-title {
    @apply text-xl sm:text-2xl font-bold text-gray-900 dark:text-white;
}

/* Form subtitle */
.custom-auth-form-subtitle {
    @apply text-sm sm:text-base text-gray-500 dark:text-gray-400;
}

/* Form fields container */
.custom-auth-form-fields {
    @apply space-y-4 sm:space-y-6;
}

/* Form field */
.custom-auth-form-field {
    @apply space-y-1.5;
}

/* Form buttons */
.custom-auth-form-button {
    @apply w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-800 dark:focus:ring-indigo-600;
}

/* Form footer */
.custom-auth-form-footer {
    @apply mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row items-center justify-between gap-3 sm:gap-4 text-sm dark:text-gray-300;
}

/* Responsive adjustments for small devices */
@media (max-width: 640px) {
    .custom-auth-form-wrapper {
        @apply py-4;
    }

    /* Mostrar imagen de fondo en móvil también */
    .custom-auth-wrapper {
        background-image: url('/img/page.jpg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        position: relative;
    }

    /* Overlay para móvil */
    .custom-auth-wrapper::before {
        content: '';
        position: absolute;
        inset: 0;
        background-color: rgb(67 56 202 / 0.8);
        z-index: 0;
    }

    /* Asegurar que el contenido esté por encima del overlay */
    .custom-auth-wrapper>* {
        position: relative;
        z-index: 1;
    }
}

/* Responsive adjustments for tablets */
@media (min-width: 641px) and (max-width: 1023px) {
    .custom-auth-wrapper {
        background-image: url('/img/page.jpg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        position: relative;
    }

    .custom-auth-wrapper::before {
        content: '';
        position: absolute;
        inset: 0;
        background-color: rgb(67 56 202 / 0.7);
        z-index: 0;
    }

    .custom-auth-wrapper>* {
        position: relative;
        z-index: 1;
    }
}

/* Responsive adjustments for extra small devices */
@media (max-width: 375px) {
    .custom-auth-form-title {
        @apply text-lg;
    }

    .custom-auth-form-subtitle {
        @apply text-xs;
    }
}