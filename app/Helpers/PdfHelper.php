<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;

class PdfHelper
{
    /**
     * Formatea un número como moneda peruana
     */
    public static function formatCurrency($amount): string
    {
        return 'S/. ' . number_format($amount ?? 0, 2);
    }

    /**
     * Formatea un número con separadores de miles
     */
    public static function formatNumber($number): string
    {
        return number_format($number ?? 0);
    }

    /**
     * Formatea kilometraje
     */
    public static function formatMileage($mileage): string
    {
        return self::formatNumber($mileage) . ' km';
    }

    /**
     * Procesa una imagen para PDF
     */
    public static function processImageForPdf($filePath): ?array
    {
        if (!$filePath || !Storage::disk('public')->exists($filePath)) {
            return null;
        }

        $fullPath = storage_path('app/public/' . $filePath);

        if (!file_exists($fullPath) || !is_readable($fullPath)) {
            return null;
        }

        $mimeType = mime_content_type($fullPath);

        // Validar tipo de imagen permitido
        if (!self::isImageTypeAllowed($mimeType)) {
            return null;
        }

        return [
            'data' => base64_encode(file_get_contents($fullPath)),
            'mime' => $mimeType,
        ];
    }

    /**
     * Obtiene las opciones por defecto para PDFs
     */
    public static function getDefaultPdfOptions(): array
    {
        return config('pdf.default_options', [
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => true,
            'defaultFont' => 'Arial',
            'dpi' => 150,
            'defaultPaperSize' => 'A4',
        ]);
    }

    /**
     * Obtiene opciones específicas para un tipo de reporte
     */
    public static function getReportOptions(string $reportType): array
    {
        return config("pdf.reports.{$reportType}", []);
    }

    /**
     * Genera un nombre de archivo para PDF
     */
    public static function generatePdfFilename(string $prefix, string $identifier): string
    {
        $config = config('pdf.filename');
        $separator = $config['separator'] ?? '-';
        $dateFormat = $config['date_format'] ?? 'Y-m-d';
        $extension = $config['extension'] ?? '.pdf';

        $filename = "{$prefix}{$separator}{$identifier}{$separator}" . now()->format($dateFormat) . $extension;

        // Limitar longitud si está configurado
        if (isset($config['max_length']) && strlen($filename) > $config['max_length']) {
            $maxLength = $config['max_length'] - strlen($extension);
            $filename = substr($filename, 0, $maxLength) . $extension;
        }

        return $filename;
    }

    /**
     * Valida si un tipo de imagen está permitido
     */
    public static function isImageTypeAllowed(string $mimeType): bool
    {
        $allowedTypes = config('pdf.images.allowed_types', ['image/jpeg', 'image/png', 'image/gif']);
        return in_array($mimeType, $allowedTypes);
    }
}
