<?php

namespace App\Http\Controllers;

use App\Helpers\PdfHelper;
use App\Models\Vehicle;
use App\Traits\PdfGenerationTrait;

class ValueMaintenanceVehicleController extends Controller
{
    use PdfGenerationTrait;
    /**
     * Handle the incoming request.
     */
    public function __invoke($id)
    {
        $record = Vehicle::with(['maintenances.maintenanceItem'])->findOrFail($id);

        // Pre-procesar las imágenes para optimizar el rendimiento
        $processedMaintenances = $this->processMaintenanceImages($record->maintenances);

        // Calcular totales
        $totals = $this->calculateMaintenanceTotals($record->maintenances);

        // Datos para la vista
        $data = [
            'record' => $record,
            'processedMaintenances' => $processedMaintenances,
            'total' => $totals['total_cost'],
            'totals' => $totals,
        ];

        // Generar PDF usando el trait
        return $this->streamPdf(
            'pdf.value-mantenace-vehicle',
            $data,
            PdfHelper::generatePdfFilename('mantenimiento', $record->placa)
        );
    }
}
