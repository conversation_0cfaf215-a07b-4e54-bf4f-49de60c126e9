<?php

namespace App\Traits;

use App\Helpers\PdfHelper;
use Barryvdh\DomPDF\Facade\Pdf;

trait PdfGenerationTrait
{
    /**
     * Genera un PDF con configuración optimizada
     */
    protected function generatePdf(string $view, array $data, array $options = []): \Barryvdh\DomPDF\PDF
    {
        $defaultOptions = PdfHelper::getDefaultPdfOptions();
        $mergedOptions = array_merge($defaultOptions, $options);

        return Pdf::loadView($view, $data)
            ->setPaper($mergedOptions['defaultPaperSize'] ?? 'A4', 'portrait')
            ->setOptions($mergedOptions);
    }

    /**
     * Genera y descarga un PDF
     */
    protected function downloadPdf(string $view, array $data, string $filename, array $options = [])
    {
        $pdf = $this->generatePdf($view, $data, $options);
        return $pdf->download($filename);
    }

    /**
     * Genera y muestra un PDF en el navegador
     */
    protected function streamPdf(string $view, array $data, string $filename, array $options = [])
    {
        $pdf = $this->generatePdf($view, $data, $options);
        return $pdf->stream($filename);
    }

    /**
     * Pre-procesa imágenes de mantenimiento para PDFs
     */
    protected function processMaintenanceImages($maintenances): \Illuminate\Support\Collection
    {
        return $maintenances->map(function ($maintenance) {
            $images = [];

            // Procesar foto
            if ($maintenance->photo) {
                $image = PdfHelper::processImageForPdf($maintenance->photo);
                if ($image) {
                    $images[] = $image;
                }
            }

            // Procesar archivo
            if ($maintenance->file) {
                $image = PdfHelper::processImageForPdf($maintenance->file);
                if ($image) {
                    $images[] = $image;
                }
            }

            return [
                'maintenance' => $maintenance,
                'images' => $images,
            ];
        })->filter(function ($item) {
            return !empty($item['images']);
        });
    }

    /**
     * Calcula totales de mantenimiento
     */
    protected function calculateMaintenanceTotals($maintenances): array
    {
        return [
            'total_cost' => $maintenances->sum('maintenance_cost'),
            'total_material' => $maintenances->sum('Price_material'),
            'total_workforce' => $maintenances->sum('workforce'),
            'count' => $maintenances->count(),
        ];
    }
}
