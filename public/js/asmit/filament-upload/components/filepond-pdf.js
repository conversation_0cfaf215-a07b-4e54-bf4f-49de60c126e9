var u=r=>r.type==="application/pdf",y=r=>new URLSearchParams({toolbar:r.query("GET_PDF_TOOLBAR")?1:0,navpanes:r.query("GET_PDF_NAVPANE")?1:0,statusbar:r.query("GET_PDF_STATUS_BAR")?1:0,zoom:r.query("GET_PDF_ZOOM")?1:0,view:r.query("GET_PDF_VIEW"),scrollbar:r.query("GET_PDF_SCROLLBAR")?1:0,page:r.query("GET_PDF_DISPLAY_PAGE")}),I=r=>{let a=({root:e,props:n})=>{let{id:c}=n,t=e.query("GET_ITEM",c),p=y(e),d=()=>{if(typeof t.source!="string"){let i=window.URL||window.webkitURL,l=new Blob([t.file],{type:t.file.type});return i.createObjectURL(l)}return t.source};e.ref.pdf.src=`${d()}#${p.toString()}`,e.ref.pdf.addEventListener("load",()=>{e.dispatch("DID_UPDATE_PANEL_HEIGHT",{id:c,height:e.ref.pdf.scrollHeight})},!1)},s=({root:e,props:n})=>{e.query("GET_ITEM",n.id).file&&(e.ref.pdf=document.createElement("iframe"),e.ref.pdf.setAttribute("height",e.query("GET_PDF_PREVIEW_HEIGHT")||320),e.ref.pdf.classList.add("filepond--pdf-preview-iframe"),e.element.appendChild(e.ref.pdf))};return r.createView({name:"pdf-preview",tag:"div",create:s,write:r.createRoute({DID_PDF_PREVIEW_LOAD:a})})},T=r=>r.createView({name:"overlay-shadow",tag:"div",ignoreRect:!0,create:({root:a,props:s})=>{a.element.classList.add(`overlay-shadow--${s.status}`)},mixins:{styles:["opacity"],animations:{opacity:{type:"spring",mass:25}}}}),D=r=>{let a=r.utils,s=I(a),e=T(a),n=({root:i,props:l})=>{let{id:f}=l;i.dispatch("DID_PDF_PREVIEW_LOAD",{id:f})},c=({root:i,props:l})=>{i.ref.overlayShadow=i.appendChildView(i.createChildView(e,{opacity:1,status:"idle"})),i.ref.overlaySuccess=i.appendChildView(i.createChildView(e,{opacity:0,status:"success"})),i.ref.overlayError=i.appendChildView(i.createChildView(e,{opacity:0,status:"failure"})),i.ref.pdf=i.appendChildView(i.createChildView(s,{id:l.id}))},t=({root:i})=>{i.ref.overlayShadow.opacity=1,i.ref.overlayError.opacity=0,i.ref.overlaySuccess.opacity=0},p=({root:i,props:l})=>{i.ref.overlayShadow.opacity=.1,i.ref.overlayError.opacity=1},d=({root:i,props:l})=>{i.ref.overlayShadow.opacity=.1,i.ref.overlaySuccess.opacity=1};return a.createView({name:"pdf-preview-wrapper",tag:"div",create:c,write:a.createRoute({DID_CREATE_PDF_PREVIEW_CONTAINER_VIEW:n,DID_THROW_ITEM_LOAD_ERROR:p,DID_THROW_ITEM_PROCESSING_ERROR:p,DID_THROW_ITEM_INVALID:p,DID_COMPLETE_ITEM_PROCESSING:d,DID_START_ITEM_PROCESSING:t,DID_REVERT_ITEM_PROCESSING:t})})},v=r=>{let{addFilter:a,utils:s}=r,{Type:e,createRoute:n}=s,c=D(r);return a("CREATE_VIEW",t=>{let{is:p,view:d,query:i}=t;if(!p("file"))return;let l=({root:f,props:w})=>{let{id:E}=w,_=i("GET_ITEM",E),o=i("GET_ALLOW_PDF_PREVIEW");!_||!u(_?.file)||_?.archived||!o||(f.ref.pdfPreview=d.appendChildView(d.createChildView(c,{id:E})),f.dispatch("DID_CREATE_PDF_PREVIEW_CONTAINER_VIEW",{id:E}))};d.registerWriter(n({DID_LOAD_ITEM:l}),(f,w)=>{f.ref.pdfPreview&&f.rect.element.hidden})}),{options:{allowPdfPreview:[!0,e.BOOLEAN],pdfPreviewHeight:[400,e.INT],pdfToolbar:[!1,e.BOOLEAN],pdfNavPanes:[!1,e.BOOLEAN],pdfStatusBar:[!1,e.BOOLEAN],pdfZoom:[!1,e.BOOLEAN],pdfView:["fitW",e.STRING],pdfDisplayPage:[1,e.INT]}}},P=v;function R({pdfPreviewHeight:r,pdfScrollbar:a,pdfDisplayPage:s,pdfToolbar:e,pdfNavPanes:n,pdfZoom:c,pdfView:t,allowPdfPreview:p}){document.addEventListener("FilePond:loaded",function(){let d=window.FilePond;d.registerPlugin(P),d.setOptions({pdfPreviewHeight:r,pdfScrollbar:a,pdfDisplayPage:s,pdfToolbar:e,pdfNavPanes:n,pdfZoom:c,pdfView:t,allowPdfPreview:p})})}export{R as default};
