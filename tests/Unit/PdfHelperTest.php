<?php

namespace Tests\Unit;

use App\Helpers\PdfHelper;
use Tests\TestCase;

class PdfHelperTest extends TestCase
{
    /** @test */
    public function it_formats_currency_correctly()
    {
        $this->assertEquals('S/. 1,234.56', PdfHelper::formatCurrency(1234.56));
        $this->assertEquals('S/. 0.00', PdfHelper::formatCurrency(null));
        $this->assertEquals('S/. 0.00', PdfHelper::formatCurrency(0));
        $this->assertEquals('S/. 100.00', PdfHelper::formatCurrency(100));
    }

    /** @test */
    public function it_formats_numbers_correctly()
    {
        $this->assertEquals('1,234', PdfHelper::formatNumber(1234));
        $this->assertEquals('0', PdfHelper::formatNumber(null));
        $this->assertEquals('0', PdfHelper::formatNumber(0));
        $this->assertEquals('1,000,000', PdfHelper::formatNumber(1000000));
    }

    /** @test */
    public function it_formats_mileage_correctly()
    {
        $this->assertEquals('15,000 km', PdfHelper::formatMileage(15000));
        $this->assertEquals('0 km', PdfHelper::formatMileage(null));
        $this->assertEquals('1,234 km', PdfHelper::formatMileage(1234));
    }

    /** @test */
    public function it_generates_pdf_filename_correctly()
    {
        $filename = PdfHelper::generatePdfFilename('test', 'ABC-123');
        
        $this->assertStringStartsWith('test-ABC-123-', $filename);
        $this->assertStringEndsWith('.pdf', $filename);
        $this->assertMatchesRegularExpression('/test-ABC-123-\d{4}-\d{2}-\d{2}\.pdf/', $filename);
    }

    /** @test */
    public function it_validates_image_types_correctly()
    {
        $this->assertTrue(PdfHelper::isImageTypeAllowed('image/jpeg'));
        $this->assertTrue(PdfHelper::isImageTypeAllowed('image/png'));
        $this->assertTrue(PdfHelper::isImageTypeAllowed('image/gif'));
        
        $this->assertFalse(PdfHelper::isImageTypeAllowed('application/pdf'));
        $this->assertFalse(PdfHelper::isImageTypeAllowed('text/plain'));
        $this->assertFalse(PdfHelper::isImageTypeAllowed('video/mp4'));
    }

    /** @test */
    public function it_gets_default_pdf_options()
    {
        $options = PdfHelper::getDefaultPdfOptions();
        
        $this->assertIsArray($options);
        $this->assertArrayHasKey('isHtml5ParserEnabled', $options);
        $this->assertArrayHasKey('isPhpEnabled', $options);
        $this->assertArrayHasKey('defaultFont', $options);
    }

    /** @test */
    public function it_gets_report_options()
    {
        $options = PdfHelper::getReportOptions('maintenance');
        
        $this->assertIsArray($options);
        // Las opciones específicas dependen de la configuración
    }

    /** @test */
    public function it_handles_null_values_gracefully()
    {
        $this->assertEquals('S/. 0.00', PdfHelper::formatCurrency(null));
        $this->assertEquals('0', PdfHelper::formatNumber(null));
        $this->assertEquals('0 km', PdfHelper::formatMileage(null));
    }

    /** @test */
    public function it_handles_negative_values()
    {
        $this->assertEquals('S/. -100.00', PdfHelper::formatCurrency(-100));
        $this->assertEquals('-1,000', PdfHelper::formatNumber(-1000));
        $this->assertEquals('-500 km', PdfHelper::formatMileage(-500));
    }

    /** @test */
    public function it_handles_large_numbers()
    {
        $this->assertEquals('S/. 1,000,000.00', PdfHelper::formatCurrency(1000000));
        $this->assertEquals('1,000,000', PdfHelper::formatNumber(1000000));
        $this->assertEquals('1,000,000 km', PdfHelper::formatMileage(1000000));
    }
}
