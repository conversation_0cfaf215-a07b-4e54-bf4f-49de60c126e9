<?php

namespace Tests\Feature;

use App\Models\Vehicle;
use App\Models\Maintenance;
use App\Models\MaintenanceItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Response;
use Tests\TestCase;

class ValueMaintenanceVehicleControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Crear datos de prueba
        $this->vehicle = Vehicle::factory()->create([
            'placa' => 'ABC-123',
            'marca' => 'Toyota',
            'unidad' => 'Camioneta',
            'property_card' => 'TC-001',
        ]);

        $this->maintenanceItem = MaintenanceItem::factory()->create([
            'name' => 'Cambio de aceite',
        ]);

        $this->maintenance = Maintenance::factory()->create([
            'vehicle_id' => $this->vehicle->id,
            'maintenance_item_id' => $this->maintenanceItem->id,
            'mileage' => 15000,
            'Price_material' => 150.00,
            'workforce' => 50.00,
            'maintenance_cost' => 200.00,
        ]);
    }

    /** @test */
    public function it_can_generate_maintenance_value_pdf()
    {
        $response = $this->get(route('valuemantenacevehicle', $this->vehicle->id));

        $response->assertStatus(Response::HTTP_OK);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    /** @test */
    public function it_returns_404_for_non_existent_vehicle()
    {
        $response = $this->get(route('valuemantenacevehicle', 999));

        $response->assertStatus(Response::HTTP_NOT_FOUND);
    }

    /** @test */
    public function it_includes_vehicle_information_in_pdf()
    {
        $response = $this->get(route('valuemantenacevehicle', $this->vehicle->id));

        $response->assertStatus(Response::HTTP_OK);
        
        // Verificar que el PDF se genera correctamente
        $this->assertNotEmpty($response->getContent());
    }

    /** @test */
    public function it_calculates_totals_correctly()
    {
        // Crear múltiples mantenimientos
        Maintenance::factory()->create([
            'vehicle_id' => $this->vehicle->id,
            'maintenance_item_id' => $this->maintenanceItem->id,
            'maintenance_cost' => 300.00,
        ]);

        $response = $this->get(route('valuemantenacevehicle', $this->vehicle->id));

        $response->assertStatus(Response::HTTP_OK);
        
        // El total debería ser 200 + 300 = 500
        $expectedTotal = 500.00;
        
        // Verificar que el controlador procesa correctamente los datos
        $this->assertTrue(true); // Placeholder - en un test real verificaríamos el contenido del PDF
    }

    /** @test */
    public function it_handles_vehicle_without_maintenances()
    {
        $vehicleWithoutMaintenance = Vehicle::factory()->create([
            'placa' => 'XYZ-789',
        ]);

        $response = $this->get(route('valuemantenacevehicle', $vehicleWithoutMaintenance->id));

        $response->assertStatus(Response::HTTP_OK);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    /** @test */
    public function it_generates_correct_filename()
    {
        $response = $this->get(route('valuemantenacevehicle', $this->vehicle->id));

        $response->assertStatus(Response::HTTP_OK);
        
        $contentDisposition = $response->headers->get('Content-Disposition');
        $this->assertStringContains('mantenimiento-ABC-123', $contentDisposition ?? '');
    }
}
