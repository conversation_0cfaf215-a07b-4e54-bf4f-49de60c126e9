<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('maintenances', function (Blueprint $table) {
            // Índice compuesto para optimizar las consultas del PDF
            $table->index(['vehicle_id', 'maintenance_item_id', 'mileage'], 'idx_maintenance_lookup');
            
            // Índice para consultas por kilometraje
            $table->index('mileage', 'idx_mileage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('maintenances', function (Blueprint $table) {
            $table->dropIndex('idx_maintenance_lookup');
            $table->dropIndex('idx_mileage');
        });
    }
};
