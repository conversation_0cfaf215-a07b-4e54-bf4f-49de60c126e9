<?php

namespace Database\Factories;

use App\Models\MaintenanceItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MaintenanceItem>
 */
class MaintenanceItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $maintenanceItems = [
            'FILTRO DE ACEITE DE MOTOR',
            'FILTRO DE COMBUSTIBLE',
            'FILTRO DE AIRE',
            'FILTRO P/POLVO A/C',
            'FILTRO TAMIZ',
            'ANILLO TAPON DE CARTER',
            'ACEITE SINTETICO - MOTOR',
            'ACEITE DE CAJA DE CAMBIOS',
            'ACEI<PERSON> DIFERENCIAL',
            'ACEITE DE DIRECCION ATF',
            'LIQUIDO REFRIG. PARA MOTOR',
            'LIQUIDO PARA FRENOS/EMBRIAGUE',
            'CONCENTRADO LAVACRISTALES',
            'CAMBIO DE ACEITE',
            'REVISI<PERSON> DE FRENOS',
            'ALINEACION Y BALANCEO',
            '<PERSON>MB<PERSON> DE LLANTAS',
            'REVISION GENERAL',
        ];

        return [
            'name' => $this->faker->randomElement($maintenanceItems),
        ];
    }
}
