<?php

namespace Database\Factories;

use App\Models\Maintenance;
use App\Models\Vehicle;
use App\Models\MaintenanceItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Maintenance>
 */
class MaintenanceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $priceMaterial = $this->faker->randomFloat(2, 50, 500);
        $workforce = $this->faker->randomFloat(2, 30, 200);
        $maintenanceCost = $priceMaterial + $workforce;

        return [
            'vehicle_id' => Vehicle::factory(),
            'maintenance_item_id' => MaintenanceItem::factory(),
            'mileage' => $this->faker->numberBetween(1000, 200000),
            'status' => $this->faker->boolean(80), // 80% true, 20% false
            'Price_material' => $priceMaterial,
            'workforce' => $workforce,
            'maintenance_cost' => $maintenanceCost,
            'photo' => null, // Por defecto null, se puede sobrescribir en tests
            'file' => null, // Por defecto null, se puede sobrescribir en tests
            'front_left_brake_pad' => $this->faker->numberBetween(0, 100),
            'front_right_brake_pad' => $this->faker->numberBetween(0, 100),
            'rear_left_brake_pad' => $this->faker->numberBetween(0, 100),
            'rear_right_brake_pad' => $this->faker->numberBetween(0, 100),
            'brake_pads_checked_at' => $this->faker->optional(0.3)->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Estado para mantenimiento completado
     */
    public function completed(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => true,
        ]);
    }

    /**
     * Estado para mantenimiento pendiente
     */
    public function pending(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Estado con archivos de prueba
     */
    public function withFiles(): static
    {
        return $this->state(fn(array $attributes) => [
            'photo' => 'maintenance/photos/test-photo.jpg',
            'file' => 'maintenance/files/test-file.pdf',
        ]);
    }

    /**
     * Estado con datos de pastillas de freno
     */
    public function withBrakePads(): static
    {
        return $this->state(fn(array $attributes) => [
            'front_left_brake_pad' => $this->faker->numberBetween(20, 100),
            'front_right_brake_pad' => $this->faker->numberBetween(20, 100),
            'rear_left_brake_pad' => $this->faker->numberBetween(20, 100),
            'rear_right_brake_pad' => $this->faker->numberBetween(20, 100),
            'brake_pads_checked_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ]);
    }
}
